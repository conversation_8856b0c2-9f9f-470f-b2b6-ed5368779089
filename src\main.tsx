import { createRoot } from 'react-dom/client'
import { App } from './App'
// Tailwind CSS is automatically injected by @tailwindcss/vite plugin
// import './styles/styles.scss' // Commented out - not needed with Tailwind v4 Vite plugin

const rootElement: HTMLElement | null = document.querySelector('#root')

if (!rootElement) {
    throw new Error('Root element not found')
}

const root = createRoot(rootElement)

const app: React.ReactElement = <App />
root.render(app)